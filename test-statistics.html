<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>抽奖统计测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .statistics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }
      .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
      }
      .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .stat-label {
        font-size: 1.1em;
        opacity: 0.9;
      }
      .test-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
      }
      .test-title {
        font-size: 1.5em;
        margin-bottom: 15px;
        color: #333;
      }
      .test-result {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        font-family: monospace;
        white-space: pre-wrap;
      }
      .error {
        color: #dc3545;
      }
      .success {
        color: #28a745;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>抽奖记录统计数据测试</h1>

      <div class="test-section">
        <div class="test-title">问题说明</div>
        <div class="test-result">
          问题：ruoyi-ui抽奖记录页面中抽奖次数、中奖次数等数据统计不正确
          原因分析： 1. 前端模板使用驼峰命名：totalDraws, totalWins, winRate,
          unclaimedCount 2. 后端SQL返回下划线命名：total_draws, total_wins,
          win_rate, unclaimed_wins 3. 字段名不匹配导致前端无法正确显示统计数据
          修复方案： 1. 修改后端SQL查询，将返回字段名改为驼峰命名格式 2.
          统一字段名：unclaimedCount (前端) = unclaimedCount (后端) 3.
          添加缺失的winRate计算 4. 添加缺失的今日统计接口
        </div>
      </div>

      <div class="test-section">
        <div class="test-title">修复的SQL查询对比</div>
        <div class="test-result">
          <strong>修复前：</strong>
          select count(*) as total_draws, count(case when is_winner = '1' then 1
          end) as total_wins, count(case when is_winner = '1' and claim_status =
          '0' then 1 end) as unclaimed_wins from lottery_record

          <strong>修复后：</strong>
          select count(*) as totalDraws, count(case when is_winner = '1' then 1
          end) as totalWins, count(case when is_winner = '1' and claim_status =
          '0' then 1 end) as unclaimedCount, round(count(case when is_winner =
          '1' then 1 end) * 100.0 / count(*), 2) as winRate from lottery_record
        </div>
      </div>

      <div class="test-section">
        <div class="test-title">前端期望的数据格式</div>
        <div class="test-result">
          { "totalDraws": 150, // 总抽奖次数 "totalWins": 45, // 总中奖次数
          "unclaimedCount": 12, // 未领取奖品数量 "winRate": 30.00 // 中奖率(%)
          }
        </div>
      </div>

      <div class="test-section">
        <div class="test-title">修复的文件列表</div>
        <div class="test-result">
          <strong>后端修复：</strong>
          1.
          prize-draw-order-ruoyi/ruoyi-system/src/main/resources/mapper/system/LotteryRecordMapper.xml
          - 修改 selectLotteryStatistics 方法（字段名改为驼峰格式） - 修改
          selectActivityStatistics 方法 - 修改 selectMerchantStatistics 方法 -
          新增 selectTodayStatistics 方法 2.
          prize-draw-order-ruoyi/ruoyi-system/src/main/java/com/ruoyi/system/mapper/LotteryRecordMapper.java
          - 新增 selectTodayStatistics 方法声明 3.
          prize-draw-order-ruoyi/ruoyi-system/src/main/java/com/ruoyi/system/service/ILotteryRecordService.java
          - 新增 selectTodayStatistics 方法声明 4.
          prize-draw-order-ruoyi/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/LotteryRecordServiceImpl.java
          - 新增 selectTodayStatistics 方法实现 5.
          prize-draw-order-ruoyi/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/LotteryRecordController.java
          - 新增 getTodayStatistics 接口

          <strong>前端修复：</strong>
          6. prize-draw-order-ruoyi/ruoyi-ui/src/views/lottery/record/index.vue
          - 导入 getMerchantId API - 添加 currentMerchantId 数据字段 - 新增
          getCurrentMerchantId 方法 - 修改 getStatistics 方法，传递商家ID参数 -
          修改 getList 方法，传递商家ID参数 - 修改 created
          生命周期，先获取商家ID再查询数据
        </div>
      </div>

      <div class="test-section">
        <div class="test-title">商家ID过滤修复说明</div>
        <div class="test-result">
          <strong>问题：</strong>
          抽奖记录页面显示的是全部商家的统计数据，而不是当前登录商家的数据。

          <strong>修复方案：</strong>
          1. 前端页面初始化时调用 getMerchantId() API 获取当前用户的商家ID 2.
          在查询抽奖记录列表和统计信息时，都传递 merchantId 参数 3.
          后端SQL查询已支持按 merchant_id 字段过滤

          <strong>修复前的查询：</strong>
          - 统计查询：getLotteryStatistics(this.queryParams) -
          列表查询：listLotteryRecord(this.addDateRange(this.queryParams,
          this.dateRange))

          <strong>修复后的查询：</strong>
          - 统计查询：getLotteryStatistics({...this.queryParams, merchantId:
          this.currentMerchantId}) -
          列表查询：listLotteryRecord(this.addDateRange({...listParams,
          merchantId: this.currentMerchantId}, this.dateRange))
        </div>
      </div>

      <div class="test-section">
        <div class="test-title">测试步骤</div>
        <div class="test-result">
          1. 重新编译并启动后端服务 2. 使用不同商家账号登录系统 3.
          访问抽奖记录页面：http://localhost/lottery/record 4.
          验证页面只显示当前登录商家的数据： - 总抽奖次数（仅当前商家） -
          总中奖次数（仅当前商家） - 中奖率（仅当前商家） -
          未领取奖品数量（仅当前商家） 5. 切换不同商家账号登录，验证数据隔离正确
          6. 检查浏览器开发者工具网络请求，确认传递了merchantId参数
        </div>
      </div>
    </div>
  </body>
</html>
