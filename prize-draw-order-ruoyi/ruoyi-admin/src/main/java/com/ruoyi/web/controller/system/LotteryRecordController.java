package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.MerchantDataScope;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.LotteryRecord;
import com.ruoyi.system.domain.dto.DrawLotteryRequest;
import com.ruoyi.system.service.ILotteryRecordService;

/**
 * 抽奖记录管理 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lottery/record")
public class LotteryRecordController extends BaseController
{
    @Autowired
    private ILotteryRecordService lotteryRecordService;

    /**
     * 获取抽奖记录列表
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:query')")
    @MerchantDataScope(merchantAlias = "r")
    @GetMapping("/list")
    public TableDataInfo list(LotteryRecord lotteryRecord)
    {
        startPage();
        List<LotteryRecord> list = lotteryRecordService.selectLotteryRecordList(lotteryRecord);
        return getDataTable(list);
    }

    /**
     * 根据活动ID获取抽奖记录列表
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:query')")
    @GetMapping("/activity/{activityId}")
    public AjaxResult getByActivityId(@PathVariable Long activityId)
    {
        List<LotteryRecord> list = lotteryRecordService.selectLotteryRecordsByActivityId(activityId);
        return success(list);
    }

    /**
     * 根据商家ID获取抽奖记录列表
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:query')")
    @GetMapping("/merchant/{merchantId}")
    public AjaxResult getByMerchantId(@PathVariable Long merchantId)
    {
        List<LotteryRecord> list = lotteryRecordService.selectLotteryRecordsByMerchantId(merchantId);
        return success(list);
    }

    /**
     * 根据用户OpenID获取抽奖记录列表
     */
    @GetMapping("/user/{userOpenid}")
    public AjaxResult getByUserOpenid(@PathVariable String userOpenid)
    {
        List<LotteryRecord> list = lotteryRecordService.selectLotteryRecordsByUserOpenid(userOpenid);
        return success(list);
    }

    /**
     * 获取用户在指定活动的抽奖记录
     */
    @GetMapping("/user/{activityId}/{userOpenid}")
    public AjaxResult getUserActivityRecords(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        List<LotteryRecord> list = lotteryRecordService.selectUserActivityRecords(activityId, userOpenid);
        return success(list);
    }

    /**
     * 获取中奖记录列表
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:query')")
    @GetMapping("/winning")
    public TableDataInfo getWinningRecords(LotteryRecord lotteryRecord)
    {
        startPage();
        List<LotteryRecord> list = lotteryRecordService.selectWinningRecords(lotteryRecord);
        return getDataTable(list);
    }

    /**
     * 获取未领取的中奖记录列表
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:query')")
    @GetMapping("/unclaimed")
    public TableDataInfo getUnclaimedWinningRecords(LotteryRecord lotteryRecord)
    {
        startPage();
        List<LotteryRecord> list = lotteryRecordService.selectUnclaimedWinningRecords(lotteryRecord);
        return getDataTable(list);
    }

    /**
     * 导出抽奖记录列表
     */
    @Log(title = "抽奖记录", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('lottery:record:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, LotteryRecord lotteryRecord)
    {
        List<LotteryRecord> list = lotteryRecordService.selectLotteryRecordList(lotteryRecord);
        ExcelUtil<LotteryRecord> util = new ExcelUtil<LotteryRecord>(LotteryRecord.class);
        util.exportExcel(response, list, "抽奖记录数据");
    }

    /**
     * 根据记录编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:query')")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable Long recordId)
    {
        return success(lotteryRecordService.selectLotteryRecordById(recordId));
    }

    /**
     * 执行抽奖
     */
    @PostMapping("/draw")
    public AjaxResult performDraw(@Valid @RequestBody DrawLotteryRequest drawRequest,
                                HttpServletRequest request)
    {
        try
        {
            String drawIp = IpUtils.getIpAddr(request);
            LotteryRecord record = lotteryRecordService.performDraw(drawRequest.getActivityId(), drawRequest.getUserOpenid(),
                drawRequest.getUserNickname(), drawRequest.getUserAvatar(), drawRequest.getTableId(), drawIp);
            return success(record);
        }
        catch (Exception e)
        {
            return error(e.getMessage());
        }
    }

    /**
     * 领取奖品
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:edit')")
    @Log(title = "抽奖记录", businessType = BusinessType.UPDATE)
    @PostMapping("/claim/{recordId}")
    public AjaxResult claimPrize(@PathVariable Long recordId)
    {
        return toAjax(lotteryRecordService.claimPrize(recordId));
    }

    /**
     * 修改抽奖记录
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:edit')")
    @Log(title = "抽奖记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LotteryRecord lotteryRecord)
    {
        return toAjax(lotteryRecordService.updateLotteryRecord(lotteryRecord));
    }

    /**
     * 删除抽奖记录
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:remove')")
    @Log(title = "抽奖记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recordIds}")
    public AjaxResult remove(@PathVariable Long[] recordIds)
    {
        return toAjax(lotteryRecordService.deleteLotteryRecordByIds(recordIds));
    }

    /**
     * 统计抽奖记录数据
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(@RequestParam Map<String, Object> params)
    {
        Map<String, Object> statistics = lotteryRecordService.selectLotteryStatistics(params);
        return success(statistics);
    }

    /**
     * 统计活动的抽奖数据
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:query')")
    @GetMapping("/statistics/activity/{activityId}")
    public AjaxResult getActivityStatistics(@PathVariable Long activityId)
    {
        Map<String, Object> statistics = lotteryRecordService.selectActivityStatistics(activityId);
        return success(statistics);
    }

    /**
     * 统计商家的抽奖数据
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:query')")
    @GetMapping("/statistics/merchant/{merchantId}")
    public AjaxResult getMerchantStatistics(@PathVariable Long merchantId)
    {
        Map<String, Object> statistics = lotteryRecordService.selectMerchantStatistics(merchantId);
        return success(statistics);
    }

    /**
     * 查询用户今日抽奖次数
     */
    @GetMapping("/count/today/{activityId}/{userOpenid}")
    public AjaxResult getTodayDrawCount(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        int count = lotteryRecordService.countUserTodayDraws(activityId, userOpenid);
        return success(count);
    }

    /**
     * 查询用户总抽奖次数
     */
    @GetMapping("/count/total/{activityId}/{userOpenid}")
    public AjaxResult getTotalDrawCount(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        int count = lotteryRecordService.countUserTotalDraws(activityId, userOpenid);
        return success(count);
    }

    /**
     * 统计商家今日的抽奖数据
     */
    @PreAuthorize("@ss.hasPermi('lottery:record:query')")
    @GetMapping("/statistics/today/{merchantId}")
    public AjaxResult getTodayStatistics(@PathVariable Long merchantId)
    {
        Map<String, Object> statistics = lotteryRecordService.selectTodayStatistics(merchantId);
        return success(statistics);
    }
}
