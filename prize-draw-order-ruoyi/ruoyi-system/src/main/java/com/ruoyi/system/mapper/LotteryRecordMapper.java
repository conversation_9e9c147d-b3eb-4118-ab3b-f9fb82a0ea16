package com.ruoyi.system.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.LotteryRecord;
import org.apache.ibatis.annotations.Param;

/**
 * 抽奖记录管理 数据层
 * 
 * <AUTHOR>
 */
public interface LotteryRecordMapper
{
    /**
     * 查询抽奖记录信息
     * 
     * @param recordId 记录ID
     * @return 记录信息
     */
    public LotteryRecord selectLotteryRecordById(Long recordId);

    /**
     * 查询抽奖记录列表
     * 
     * @param lotteryRecord 记录信息
     * @return 记录集合
     */
    public List<LotteryRecord> selectLotteryRecordList(LotteryRecord lotteryRecord);

    /**
     * 根据活动ID查询抽奖记录列表
     * 
     * @param activityId 活动ID
     * @return 记录集合
     */
    public List<LotteryRecord> selectLotteryRecordsByActivityId(Long activityId);

    /**
     * 根据商家ID查询抽奖记录列表
     * 
     * @param merchantId 商家ID
     * @return 记录集合
     */
    public List<LotteryRecord> selectLotteryRecordsByMerchantId(Long merchantId);

    /**
     * 根据用户OpenID查询抽奖记录列表
     * 
     * @param userOpenid 用户OpenID
     * @return 记录集合
     */
    public List<LotteryRecord> selectLotteryRecordsByUserOpenid(String userOpenid);

    /**
     * 查询用户在指定活动的抽奖记录
     * 
     * @param activityId 活动ID
     * @param userOpenid 用户OpenID
     * @return 记录集合
     */
    public List<LotteryRecord> selectUserActivityRecords(@Param("activityId") Long activityId, @Param("userOpenid") String userOpenid);

    /**
     * 查询用户今日在指定活动的抽奖次数
     * 
     * @param activityId 活动ID
     * @param userOpenid 用户OpenID
     * @return 抽奖次数
     */
    public int countUserTodayDraws(@Param("activityId") Long activityId, @Param("userOpenid") String userOpenid);

    /**
     * 查询用户在指定活动的总抽奖次数
     * 
     * @param activityId 活动ID
     * @param userOpenid 用户OpenID
     * @return 抽奖次数
     */
    public int countUserTotalDraws(@Param("activityId")Long activityId, @Param("userOpenid")String userOpenid);

    /**
     * 查询中奖记录列表
     * 
     * @param lotteryRecord 记录信息
     * @return 记录集合
     */
    public List<LotteryRecord> selectWinningRecords(LotteryRecord lotteryRecord);

    /**
     * 查询未领取的中奖记录列表
     * 
     * @param lotteryRecord 记录信息
     * @return 记录集合
     */
    public List<LotteryRecord> selectUnclaimedWinningRecords(LotteryRecord lotteryRecord);

    /**
     * 新增抽奖记录
     * 
     * @param lotteryRecord 记录信息
     * @return 结果
     */
    public int insertLotteryRecord(LotteryRecord lotteryRecord);

    /**
     * 修改抽奖记录
     * 
     * @param lotteryRecord 记录信息
     * @return 结果
     */
    public int updateLotteryRecord(LotteryRecord lotteryRecord);

    /**
     * 删除抽奖记录
     * 
     * @param recordId 记录ID
     * @return 结果
     */
    public int deleteLotteryRecordById(Long recordId);

    /**
     * 批量删除抽奖记录
     * 
     * @param recordIds 需要删除的记录ID
     * @return 结果
     */
    public int deleteLotteryRecordByIds(Long[] recordIds);

    /**
     * 根据活动ID删除抽奖记录
     * 
     * @param activityId 活动ID
     * @return 结果
     */
    public int deleteLotteryRecordByActivityId(Long activityId);

    /**
     * 根据商家ID删除抽奖记录
     * 
     * @param merchantId 商家ID
     * @return 结果
     */
    public int deleteLotteryRecordByMerchantId(Long merchantId);

    /**
     * 统计抽奖记录数据
     * 
     * @param params 查询参数
     * @return 统计结果
     */
    public Map<String, Object> selectLotteryStatistics(Map<String, Object> params);

    /**
     * 统计活动的抽奖数据
     * 
     * @param activityId 活动ID
     * @return 统计结果
     */
    public Map<String, Object> selectActivityStatistics(Long activityId);

    /**
     * 统计商家的抽奖数据
     *
     * @param merchantId 商家ID
     * @return 统计结果
     */
    public Map<String, Object> selectMerchantStatistics(Long merchantId);

    /**
     * 统计商家今日的抽奖数据
     *
     * @param merchantId 商家ID
     * @return 统计结果
     */
    public Map<String, Object> selectTodayStatistics(Long merchantId);
}
