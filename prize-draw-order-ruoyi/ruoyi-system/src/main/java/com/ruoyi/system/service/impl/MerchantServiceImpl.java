package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.mapper.MerchantMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.IMerchantService;
import com.ruoyi.system.service.IMerchantConfigService;
import com.ruoyi.system.service.ISysUserService;

/**
 * 商家管理 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class MerchantServiceImpl implements IMerchantService
{
    @Autowired
    private MerchantMapper merchantMapper;

    @Autowired
    private IMerchantConfigService merchantConfigService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 查询商家信息
     * 
     * @param merchantId 商家ID
     * @return 商家信息
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public Merchant selectMerchantById(Long merchantId)
    {
        return merchantMapper.selectMerchantById(merchantId);
    }

    /**
     * 根据商家编码查询商家信息
     * 
     * @param merchantCode 商家编码
     * @return 商家信息
     */
    @Override
    public Merchant selectMerchantByCode(String merchantCode)
    {
        return merchantMapper.selectMerchantByCode(merchantCode);
    }

    /**
     * 查询商家列表
     * 
     * @param merchant 商家信息
     * @return 商家集合
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<Merchant> selectMerchantList(Merchant merchant)
    {
        return merchantMapper.selectMerchantList(merchant);
    }

    /**
     * 校验商家编码是否唯一
     * 
     * @param merchant 商家信息
     * @return 结果
     */
    @Override
    public boolean checkMerchantCodeUnique(Merchant merchant)
    {
        Long merchantId = StringUtils.isNull(merchant.getMerchantId()) ? -1L : merchant.getMerchantId();
        Merchant info = merchantMapper.checkMerchantCodeUnique(merchant.getMerchantCode());
        if (StringUtils.isNotNull(info) && info.getMerchantId().longValue() != merchantId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 新增商家
     *
     * @param merchant 商家信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertMerchant(Merchant merchant)
    {
        merchant.setCreateTime(DateUtils.getNowDate());
        int result = merchantMapper.insertMerchant(merchant);

        // 初始化商家默认配置
        if (result > 0 && merchant.getMerchantId() != null)
        {
            merchantConfigService.initDefaultMerchantConfig(merchant.getMerchantId());
        }

        return result;
    }

    /**
     * 修改商家
     * 
     * @param merchant 商家信息
     * @return 结果
     */
    @Override
    public int updateMerchant(Merchant merchant)
    {
        merchant.setUpdateTime(DateUtils.getNowDate());
        return merchantMapper.updateMerchant(merchant);
    }

    /**
     * 批量删除商家
     * 
     * @param merchantIds 需要删除的商家ID
     * @return 结果
     */
    @Override
    public int deleteMerchantByIds(Long[] merchantIds)
    {
        return merchantMapper.deleteMerchantByIds(merchantIds);
    }

    /**
     * 删除商家信息
     * 
     * @param merchantId 商家ID
     * @return 结果
     */
    @Override
    public int deleteMerchantById(Long merchantId)
    {
        return merchantMapper.deleteMerchantById(merchantId);
    }

    /**
     * 查询即将到期的商家列表
     * 
     * @param days 提前天数
     * @return 商家集合
     */
    @Override
    public List<Merchant> selectExpiringSoonMerchants(int days)
    {
        return merchantMapper.selectExpiringSoonMerchants(days);
    }

    /**
     * 查询已过期的商家列表
     * 
     * @return 商家集合
     */
    @Override
    public List<Merchant> selectExpiredMerchants()
    {
        return merchantMapper.selectExpiredMerchants();
    }

    /**
     * 更新过期商家状态
     * 
     * @return 结果
     */
    @Override
    public int updateExpiredMerchantsStatus()
    {
        return merchantMapper.updateExpiredMerchantsStatus();
    }

    /**
     * 检查商家是否有效（未过期且状态正常）
     * 
     * @param merchantCode 商家编码
     * @return 是否有效
     */
    @Override
    public boolean isMerchantValid(String merchantCode)
    {
        Merchant merchant = merchantMapper.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return false;
        }
        
        // 检查状态
        if (!"0".equals(merchant.getStatus()))
        {
            return false;
        }
        
        // 检查是否过期
        if (merchant.getExpireTime() != null && merchant.getExpireTime().before(new Date()))
        {
            return false;
        }
        
        return true;
    }

    /**
     * 新增商家并创建对应的用户账号
     *
     * @param merchant 商家信息
     * @param createUser 是否创建用户账号
     * @return 结果
     */
    @Override
    @Transactional
    public int insertMerchantWithUser(Merchant merchant, boolean createUser)
    {
        merchant.setCreateTime(DateUtils.getNowDate());
        int result = merchantMapper.insertMerchant(merchant);

        // 初始化商家默认配置
        if (result > 0 && merchant.getMerchantId() != null)
        {
            merchantConfigService.initDefaultMerchantConfig(merchant.getMerchantId());

            // 如果需要创建用户账号
            if (createUser)
            {
                createMerchantUser(merchant);
            }
        }

        return result;
    }

    /**
     * 修改商家并同步更新用户信息
     *
     * @param merchant 商家信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateMerchantWithUser(Merchant merchant)
    {
        merchant.setUpdateTime(DateUtils.getNowDate());
        int result = merchantMapper.updateMerchant(merchant);

        // 同步更新用户信息
        if (result > 0)
        {

            SysUser user = selectUserByMerchantId(merchant.getMerchantId());
            if (user != null)
            {
                // 设置商家角色ID（假设商家角色ID为100）
                Long[] roleIds = {100L};
                user.setRoleIds(roleIds);
                // 更新用户昵称为商家名称
                user.setNickName(merchant.getMerchantName());
                // 更新联系电话
                if (StringUtils.isNotEmpty(merchant.getContactPhone()))
                {
                    user.setPhonenumber(merchant.getContactPhone());
                }
                // 根据商家状态更新用户状态
                user.setStatus(merchant.getStatus());
                userService.updateUser(user);
            }
        }

        return result;
    }

    /**
     * 为商家创建用户账号
     *
     * @param merchant 商家信息
     * @return 用户ID
     */
    @Override
    @Transactional
    public Long createMerchantUser(Merchant merchant)
    {
        // 创建用户对象
        SysUser user = new SysUser();
        user.setUserName(merchant.getMerchantCode()); // 用户名使用商家编码
        user.setNickName(merchant.getMerchantName()); // 昵称使用商家名称
        user.setEmail(""); // 邮箱为空
        user.setPhonenumber(StringUtils.isNotEmpty(merchant.getContactPhone()) ? merchant.getContactPhone() : "");
        user.setSex("0"); // 默认男性
        user.setAvatar(""); // 头像为空
        user.setPassword(SecurityUtils.encryptPassword("123456")); // 默认密码123456
        user.setStatus(merchant.getStatus()); // 状态与商家状态一致
        user.setDelFlag("0"); // 未删除
        user.setCreateBy("admin"); // 创建者
        user.setCreateTime(DateUtils.getNowDate());
        user.setRemark("商家用户：" + merchant.getMerchantName());
        user.setMerchantId(merchant.getMerchantId()); // 设置关联的商家ID

        // 设置商家角色ID（假设商家角色ID为100）
        Long[] roleIds = {100L};
        user.setRoleIds(roleIds);

        // 创建用户
        int result = userService.insertUser(user);

        if (result > 0)
        {
            return user.getUserId();
        }

        return null;
    }

    /**
     * 根据商家ID查询关联的用户
     *
     * @param merchantId 商家ID
     * @return 用户信息
     */
    @Override
    public SysUser selectUserByMerchantId(Long merchantId)
    {
        Merchant merchant = merchantMapper.selectMerchantById(merchantId);
        if (merchant != null && StringUtils.isNotEmpty(merchant.getMerchantCode()))
        {
            return userMapper.selectUserByUserName(merchant.getMerchantCode());
        }
        return null;
    }
}
