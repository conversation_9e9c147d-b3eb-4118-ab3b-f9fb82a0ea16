<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.LotteryRecordMapper">
    
    <resultMap type="LotteryRecord" id="LotteryRecordResult">
        <id     property="recordId"       column="record_id"       />
        <result property="activityId"     column="activity_id"     />
        <result property="merchantId"     column="merchant_id"     />
        <result property="merchantCode"   column="merchant_code"   />
        <result property="tableId"        column="table_id"        />
        <result property="userOpenid"     column="user_openid"     />
        <result property="userNickname"   column="user_nickname"   />
        <result property="userAvatar"     column="user_avatar"     />
        <result property="prizeName"      column="prize_name"      />
        <result property="prizeType"      column="prize_type"      />
        <result property="prizeValue"     column="prize_value"     />
        <result property="isWinner"       column="is_winner"       />
        <result property="claimStatus"    column="claim_status"    />
        <result property="claimTime"      column="claim_time"      />
        <result property="drawTime"       column="draw_time"       />
        <result property="drawIp"         column="draw_ip"         />
        <result property="createTime"     column="create_time"     />
        <result property="remark"         column="remark"          />
        <result property="activityName"   column="activity_name"   />
        <result property="merchantName"   column="merchant_name"   />
        <result property="tableNumber"    column="table_number"    />
    </resultMap>
    
    <sql id="selectLotteryRecordVo">
        select r.record_id, r.activity_id, r.merchant_id, r.merchant_code, r.table_id, r.user_openid,
               r.user_nickname, r.user_avatar, r.prize_name, r.prize_type, r.prize_value,
               r.is_winner, r.claim_status, r.claim_time, r.draw_time, r.draw_ip,
               r.create_time, r.remark, a.activity_name, m.merchant_name, t.table_number
        from lottery_record r
        left join lottery_activity a on r.activity_id = a.activity_id
        left join merchant m on r.merchant_id = m.merchant_id
        left join merchant_table t on r.table_id = t.table_id
    </sql>
    
    <select id="selectLotteryRecordList" parameterType="LotteryRecord" resultMap="LotteryRecordResult">
        <include refid="selectLotteryRecordVo"/>
        <where>
            <if test="activityId != null">
                AND r.activity_id = #{activityId}
            </if>
            <if test="merchantId != null">
                AND r.merchant_id = #{merchantId}
            </if>
            <if test="tableId != null">
                AND r.table_id = #{tableId}
            </if>
            <if test="userOpenid != null and userOpenid != ''">
                AND r.user_openid = #{userOpenid}
            </if>
            <if test="userNickname != null and userNickname != ''">
                AND r.user_nickname like concat('%', #{userNickname}, '%')
            </if>
            <if test="prizeName != null and prizeName != ''">
                AND r.prize_name like concat('%', #{prizeName}, '%')
            </if>
            <if test="prizeType != null and prizeType != ''">
                AND r.prize_type = #{prizeType}
            </if>
            <if test="isWinner != null and isWinner != ''">
                AND r.is_winner = #{isWinner}
            </if>
            <if test="claimStatus != null and claimStatus != ''">
                AND r.claim_status = #{claimStatus}
            </if>
            <if test="activityName != null and activityName != ''">
                AND a.activity_name like concat('%', #{activityName}, '%')
            </if>
            <if test="merchantName != null and merchantName != ''">
                AND m.merchant_name like concat('%', #{merchantName}, '%')
            </if>
            <if test="tableNumber != null and tableNumber != ''">
                AND t.table_number like concat('%', #{tableNumber}, '%')
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(r.draw_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(r.draw_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            ${params.merchantDataScopeFilter}
        </where>
        order by r.record_id desc
    </select>
    
    <select id="selectLotteryRecordById" parameterType="Long" resultMap="LotteryRecordResult">
        <include refid="selectLotteryRecordVo"/>
        where r.record_id = #{recordId}
    </select>
    
    <select id="selectLotteryRecordsByActivityId" parameterType="Long" resultMap="LotteryRecordResult">
        <include refid="selectLotteryRecordVo"/>
        where r.activity_id = #{activityId}
        order by r.draw_time desc
    </select>
    
    <select id="selectLotteryRecordsByMerchantId" parameterType="Long" resultMap="LotteryRecordResult">
        <include refid="selectLotteryRecordVo"/>
        where r.merchant_id = #{merchantId}
        order by r.draw_time desc
    </select>
    
    <select id="selectLotteryRecordsByUserOpenid" parameterType="String" resultMap="LotteryRecordResult">
        <include refid="selectLotteryRecordVo"/>
        where r.user_openid = #{userOpenid}
        order by r.draw_time desc
    </select>
    
    <select id="selectUserActivityRecords" resultMap="LotteryRecordResult">
        <include refid="selectLotteryRecordVo"/>
        where r.activity_id = #{activityId} and r.user_openid = #{userOpenid}
        order by r.draw_time desc
    </select>
    
    <select id="countUserTodayDraws" resultType="int">
        select count(*) from lottery_record 
        where activity_id = #{activityId} and user_openid = #{userOpenid}
        and date_format(draw_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
    </select>
    
    <select id="countUserTotalDraws" resultType="int">
        select count(*) from lottery_record 
        where activity_id = #{activityId} and user_openid = #{userOpenid}
    </select>
    
    <select id="selectWinningRecords" parameterType="LotteryRecord" resultMap="LotteryRecordResult">
        <include refid="selectLotteryRecordVo"/>
        <where>
            r.is_winner = '1'
            <if test="activityId != null">
                AND r.activity_id = #{activityId}
            </if>
            <if test="merchantId != null">
                AND r.merchant_id = #{merchantId}
            </if>
            <if test="claimStatus != null and claimStatus != ''">
                AND r.claim_status = #{claimStatus}
            </if>
        </where>
        order by r.draw_time desc
    </select>
    
    <select id="selectUnclaimedWinningRecords" parameterType="LotteryRecord" resultMap="LotteryRecordResult">
        <include refid="selectLotteryRecordVo"/>
        <where>
            r.is_winner = '1' and r.claim_status = '0'
            <if test="activityId != null">
                AND r.activity_id = #{activityId}
            </if>
            <if test="merchantId != null">
                AND r.merchant_id = #{merchantId}
            </if>
        </where>
        order by r.draw_time desc
    </select>
    
    <insert id="insertLotteryRecord" parameterType="LotteryRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into lottery_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">activity_id,</if>
            <if test="merchantId != null">merchant_id,</if>
            <if test="merchantCode != null">merchant_code,</if>
            <if test="tableId != null">table_id,</if>
            <if test="userOpenid != null">user_openid,</if>
            <if test="userNickname != null">user_nickname,</if>
            <if test="userAvatar != null">user_avatar,</if>
            <if test="prizeName != null">prize_name,</if>
            <if test="prizeType != null">prize_type,</if>
            <if test="prizeValue != null">prize_value,</if>
            <if test="isWinner != null">is_winner,</if>
            <if test="claimStatus != null">claim_status,</if>
            <if test="claimTime != null">claim_time,</if>
            <if test="drawTime != null">draw_time,</if>
            <if test="drawIp != null">draw_ip,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">#{activityId},</if>
            <if test="merchantId != null">#{merchantId},</if>
            <if test="merchantCode != null">#{merchantCode},</if>
            <if test="tableId != null">#{tableId},</if>
            <if test="userOpenid != null">#{userOpenid},</if>
            <if test="userNickname != null">#{userNickname},</if>
            <if test="userAvatar != null">#{userAvatar},</if>
            <if test="prizeName != null">#{prizeName},</if>
            <if test="prizeType != null">#{prizeType},</if>
            <if test="prizeValue != null">#{prizeValue},</if>
            <if test="isWinner != null">#{isWinner},</if>
            <if test="claimStatus != null">#{claimStatus},</if>
            <if test="claimTime != null">#{claimTime},</if>
            <if test="drawTime != null">#{drawTime},</if>
            <if test="drawIp != null">#{drawIp},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>
    
    <update id="updateLotteryRecord" parameterType="LotteryRecord">
        update lottery_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityId != null">activity_id = #{activityId},</if>
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="merchantCode != null">merchant_code = #{merchantCode},</if>
            <if test="tableId != null">table_id = #{tableId},</if>
            <if test="userOpenid != null">user_openid = #{userOpenid},</if>
            <if test="userNickname != null">user_nickname = #{userNickname},</if>
            <if test="userAvatar != null">user_avatar = #{userAvatar},</if>
            <if test="prizeName != null">prize_name = #{prizeName},</if>
            <if test="prizeType != null">prize_type = #{prizeType},</if>
            <if test="prizeValue != null">prize_value = #{prizeValue},</if>
            <if test="isWinner != null">is_winner = #{isWinner},</if>
            <if test="claimStatus != null">claim_status = #{claimStatus},</if>
            <if test="claimTime != null">claim_time = #{claimTime},</if>
            <if test="drawTime != null">draw_time = #{drawTime},</if>
            <if test="drawIp != null">draw_ip = #{drawIp},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where record_id = #{recordId}
    </update>
    
    <delete id="deleteLotteryRecordById" parameterType="Long">
        delete from lottery_record where record_id = #{recordId}
    </delete>
    
    <delete id="deleteLotteryRecordByIds" parameterType="String">
        delete from lottery_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
    
    <delete id="deleteLotteryRecordByActivityId" parameterType="Long">
        delete from lottery_record where activity_id = #{activityId}
    </delete>
    
    <delete id="deleteLotteryRecordByMerchantId" parameterType="Long">
        delete from lottery_record where merchant_id = #{merchantId}
    </delete>
    
    <select id="selectLotteryStatistics" parameterType="map" resultType="map">
        select
            count(*) as totalDraws,
            count(case when is_winner = '1' then 1 end) as totalWins,
            count(case when is_winner = '1' and claim_status = '1' then 1 end) as totalClaims,
            count(case when is_winner = '1' and claim_status = '0' then 1 end) as unclaimedCount,
            count(distinct user_openid) as uniqueUsers,
            round(count(case when is_winner = '1' then 1 end) * 100.0 / count(*), 2) as winRate
        from lottery_record
        <where>
            <if test="merchantId != null">
                AND merchant_id = #{merchantId}
            </if>
            <if test="activityId != null">
                AND activity_id = #{activityId}
            </if>
            <if test="beginTime != null and beginTime != ''">
                AND date_format(draw_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                AND date_format(draw_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
    </select>
    
    <select id="selectActivityStatistics" parameterType="Long" resultType="map">
        select
            count(*) as totalDraws,
            count(case when is_winner = '1' then 1 end) as totalWins,
            count(case when is_winner = '1' and claim_status = '1' then 1 end) as totalClaims,
            count(case when is_winner = '1' and claim_status = '0' then 1 end) as unclaimedCount,
            count(distinct user_openid) as uniqueUsers,
            round(count(case when is_winner = '1' then 1 end) * 100.0 / count(*), 2) as winRate
        from lottery_record
        where activity_id = #{activityId}
    </select>
    
    <select id="selectMerchantStatistics" parameterType="Long" resultType="map">
        select
            count(*) as totalDraws,
            count(case when is_winner = '1' then 1 end) as totalWins,
            count(case when is_winner = '1' and claim_status = '1' then 1 end) as totalClaims,
            count(case when is_winner = '1' and claim_status = '0' then 1 end) as unclaimedCount,
            count(distinct user_openid) as uniqueUsers,
            count(distinct activity_id) as totalActivities,
            round(count(case when is_winner = '1' then 1 end) * 100.0 / count(*), 2) as winRate
        from lottery_record
        where merchant_id = #{merchantId}
    </select>

    <select id="selectTodayStatistics" parameterType="Long" resultType="map">
        select
            count(*) as totalDraws,
            count(case when is_winner = '1' then 1 end) as totalWins,
            count(case when is_winner = '1' and claim_status = '1' then 1 end) as totalClaims,
            count(case when is_winner = '1' and claim_status = '0' then 1 end) as unclaimedCount,
            count(distinct user_openid) as uniqueUsers,
            count(distinct activity_id) as totalActivities,
            round(count(case when is_winner = '1' then 1 end) * 100.0 / count(*), 2) as winRate
        from lottery_record
        where merchant_id = #{merchantId}
        and date_format(draw_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
    </select>

</mapper>
